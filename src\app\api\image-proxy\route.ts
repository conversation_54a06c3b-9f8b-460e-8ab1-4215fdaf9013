import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const originalUrl = searchParams.get('url')
    const filePath = searchParams.get('path')

    console.log('🔍 Image proxy request:', { originalUrl, filePath })

    if (!originalUrl && !filePath) {
      return NextResponse.json(
        { error: 'Either URL or file path is required' },
        { status: 400 }
      )
    }

    let pathToUse = filePath

    // If we have an original URL, extract the file path from it
    if (originalUrl && originalUrl.includes('supabase.co')) {
      try {
        console.log('📝 Parsing Supabase URL:', originalUrl)
        const urlParts = originalUrl.split('/storage/v1/object/')
        console.log('🔗 URL parts:', urlParts)
        if (urlParts.length > 1) {
          const pathPart = urlParts[1].split('?')[0] // Remove query params

          // Handle different URL formats
          if (pathPart.startsWith('public/')) {
            // This is already a public URL, extract the path after the bucket name
            // Format: public/File/File/id-cards/... -> File/id-cards/...
            pathToUse = pathPart.replace('public/File/', '')
          } else if (pathPart.startsWith('sign/')) {
            // This is a signed URL, remove the sign/ prefix
            pathToUse = pathPart.replace('sign/', '')
          } else {
            // Fallback: use the path as-is
            pathToUse = pathPart
          }

          console.log('📁 Extracted path:', pathToUse)
        }
      } catch (error) {
        console.error('Error parsing original URL:', error)
      }
    }

    if (!pathToUse) {
      console.error('❌ Could not determine file path from:', { originalUrl, filePath })
      return NextResponse.json(
        { error: 'Could not determine file path' },
        { status: 400 }
      )
    }

    console.log('🎯 Using path:', pathToUse)

    // Since the bucket is public, use public URL (no expiration)
    const { data: publicData } = supabaseAdmin.storage
      .from('File')
      .getPublicUrl(pathToUse)

    console.log('🌐 Public URL result:', publicData)

    if (publicData?.publicUrl) {
      console.log('✅ Returning public URL:', publicData.publicUrl)
      return NextResponse.json({
        signedUrl: publicData.publicUrl,
        isPublic: true,
        expiresAt: null // Public URLs don't expire
      })
    }

    // Fallback: try to generate a signed URL if public URL fails
    const { data, error } = await supabaseAdmin.storage
      .from('File')
      .createSignedUrl(pathToUse, 3600) // 1 hour expiration

    if (error) {
      console.error('Error creating signed URL:', error)
      return NextResponse.json(
        { error: 'Failed to generate image URL' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      signedUrl: data.signedUrl,
      isPublic: false,
      expiresAt: new Date(Date.now() + 3600 * 1000).toISOString()
    })
  } catch (error) {
    console.error('Error in image proxy:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
