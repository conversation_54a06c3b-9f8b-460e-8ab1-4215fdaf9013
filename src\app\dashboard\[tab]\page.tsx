import { notFound } from 'next/navigation'
import FeeManagementForm from '@/components/FeeManagementForm'
import FeeRecordsComponent from '@/components/FeeRecordsComponent'
import PendingFeesComponent from '@/components/PendingFeesComponent'
import AttendanceManagement from '@/components/AttendanceManagement'
import DashboardAnalytics from '@/components/DashboardAnalytics'
import BirthdayManagement from '@/components/BirthdayManagement'
import StudentManagement from '@/components/StudentManagement'

interface PageProps {
  params: Promise<{
    tab: string
  }>
}

// Valid tab types
const validTabs = ['dashboard', 'attendance', 'collection', 'records', 'pending', 'birthdays', 'students'] as const
type TabType = typeof validTabs[number]

// Tab configuration
const tabConfig = {
  dashboard: { name: 'Dashboard', description: 'Overview of attendance and fees', component: DashboardAnalytics },
  attendance: { name: 'Attendance', description: 'Mark and manage student attendance', component: AttendanceManagement },
  collection: { name: 'Fee Collection', description: 'Record new fee payments', component: FeeManagementForm },
  records: { name: 'Fee Records', description: 'View all fee submission records', component: FeeRecordsComponent },
  pending: { name: 'Pending Fees', description: 'View students with pending fees', component: PendingFeesComponent },
  birthdays: { name: 'Birthdays', description: 'Celebrate student birthdays', component: BirthdayManagement },
  students: { name: 'Students', description: 'Manage all student records and information', component: StudentManagement },
}

export default async function TabPage({ params }: PageProps) {
  const { tab } = await params
  
  // Validate tab parameter
  if (!validTabs.includes(tab as TabType)) {
    notFound()
  }
  
  const tabInfo = tabConfig[tab as TabType]
  const Component = tabInfo.component
  
  return (
    <div className="min-h-screen bg-white">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-2xl font-bold text-black">
          {tabInfo.name}
        </h2>
        <p className="mt-1 text-sm text-gray-600">
          {tabInfo.description}
        </p>
      </div>
      <div className="p-6">
        <Component />
      </div>
    </div>
  )
}

// Generate static params for better performance
export function generateStaticParams() {
  return validTabs.map((tab) => ({
    tab: tab,
  }))
}
